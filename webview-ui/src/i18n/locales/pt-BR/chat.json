{"greeting": "Bem-vindo ao Roo <PERSON>", "task": {"title": "<PERSON><PERSON><PERSON>", "seeMore": "Ver mais", "seeLess": "<PERSON>er menos", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "Custo da API:", "contextWindow": "<PERSON><PERSON>:", "closeAndStart": "Fechar tarefa e iniciar nova", "export": "Exportar histó<PERSON><PERSON> de <PERSON>fas", "delete": "Excluir tarefa (Shift + Clique para pular confirmação)", "condenseContext": "Condensar contexto de forma inteligente"}, "unpin": "Desfixar", "pin": "Fixar", "tokenProgress": {"availableSpace": "Espaço disponível: {{amount}} tokens", "tokensUsed": "Tokens usados: {{used}} de {{total}}", "reservedForResponse": "Reservado para resposta do modelo: {{amount}} tokens"}, "retry": {"title": "Tentar novamente", "tooltip": "Tentar a operação novamente"}, "startNewTask": {"title": "Iniciar nova tarefa", "tooltip": "Começar uma nova tarefa"}, "proceedAnyways": {"title": "Prosseguir mesmo assim", "tooltip": "Continuar enquanto o comando executa"}, "save": {"title": "<PERSON><PERSON>", "tooltip": "Salvar as alterações do arquivo"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Rejeitar esta ação"}, "completeSubtaskAndReturn": "Completar subtarefa e retornar", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Aprovar esta ação"}, "runCommand": {"title": "Executar comando", "tooltip": "Executar este comando"}, "proceedWhileRunning": {"title": "Prosseguir durante execução", "tooltip": "Continuar apesar dos avisos"}, "killCommand": {"title": "Interromper Comando", "tooltip": "Interromper o comando atual"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON> ta<PERSON>", "tooltip": "Continuar a tarefa atual"}, "terminate": {"title": "Terminar", "tooltip": "Encerrar a tarefa atual"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Cancelar a operação atual"}, "scrollToBottom": "Rolar para o final do chat", "about": "<PERSON><PERSON>, refatore e depure código com assistência de IA.<br /><PERSON><PERSON><PERSON> nossa <DocsLink>documentação</DocsLink> para saber mais.", "onboarding": "<strong>Sua lista de tarefas neste espaço de trabalho está vazia.</strong> Comece digitando uma tarefa abaixo. Não sabe como começar? Leia mais sobre o que o Roo pode fazer por você nos <DocsLink>documentos</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> as tarefas em partes menores e gerenciáveis."}, "stickyModels": {"title": "Modos Fixos", "description": "Cada modo lembra o seu último modelo usado"}, "tools": {"title": "Ferramentas", "description": "Permita que a IA resolva problemas navegando na web, executando comandos e muito mais."}, "customizableModes": {"title": "Modos personalizáveis", "description": "Personas especializadas com comportamentos próprios e modelos atribuídos"}}, "selectMode": "Selecionar modo de interação", "selectApiConfig": "Selecionar configuração da API", "enhancePrompt": "Aprimorar prompt com contexto adicional", "addImages": "Adicionar imagens à mensagem", "sendMessage": "Enviar mensagem", "typeMessage": "Digite uma mensagem...", "typeTask": "Digite sua tarefa aqui...", "addContext": "@ para adicionar contexto, / para alternar modos", "dragFiles": "segure shift para arrastar arquivos", "dragFilesImages": "segure shift para arrastar arquivos/imagens", "enhancePromptDescription": "O botão 'Aprimorar prompt' ajuda a melhorar seu pedido fornecendo contexto adicional, esclarecimentos ou reformulações. Tente digitar um pedido aqui e clique no botão novamente para ver como funciona.", "errorReadingFile": "Erro ao ler arquivo:", "noValidImages": "Nenhuma imagem válida foi processada", "separator": "Separador", "edit": "Editar...", "forNextMode": "para o próximo modo", "error": "Erro", "diffError": {"title": "Edição mal-sucedida"}, "troubleMessage": "Roo está tendo problemas...", "apiRequest": {"title": "Requisição API", "failed": "Requisição API falhou", "streaming": "Requisição API...", "cancelled": "Requisição API cancelada", "streamingFailed": "Streaming API falhou"}, "checkpoint": {"initial": "Ponto de verificação inicial", "regular": "Ponto de verificação", "initializingWarning": "Ainda inicializando ponto de verificação... Se isso demorar muito, você pode desativar os pontos de verificação nas <settingsLink>configurações</settingsLink> e reiniciar sua tarefa.", "menu": {"viewDiff": "Ver diferenças", "restore": "Restaurar ponto de verificação", "restoreFiles": "Restaurar a<PERSON>", "restoreFilesDescription": "Restaura os arquivos do seu projeto para um snapshot feito neste ponto.", "restoreFilesAndTask": "Restaurar arquivos e tarefa", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Esta ação não pode ser desfeita.", "restoreFilesAndTaskDescription": "Restaura os arquivos do seu projeto para um snapshot feito neste ponto e exclui todas as mensagens após este ponto."}, "current": "Atual"}, "instructions": {"wantsToFetch": "Roo quer buscar instruções detalhadas para ajudar com a tarefa atual"}, "fileOperations": {"wantsToRead": "Roo quer ler este arquivo:", "wantsToReadOutsideWorkspace": "Roo quer ler este arquivo fora do espaço de trabalho:", "didRead": "Roo leu este arquivo:", "wantsToEdit": "<PERSON>oo quer editar este arquivo:", "wantsToEditOutsideWorkspace": "<PERSON>oo quer editar este arquivo fora do espaço de trabalho:", "wantsToCreate": "Roo quer criar um novo arquivo:", "wantsToSearchReplace": "Roo quer realizar busca e substituição neste arquivo:", "didSearchReplace": "Roo realizou busca e substituição neste arquivo:", "wantsToInsert": "Roo quer inserir conteúdo neste arquivo:", "wantsToInsertWithLineNumber": "Roo quer inserir conteúdo neste arquivo na linha {{lineNumber}}:", "wantsToInsertAtEnd": "<PERSON>oo quer adicionar conteúdo ao final deste arquivo:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON>oo quer visualizar os arquivos de nível superior neste diretório:", "didViewTopLevel": "Roo visualizou os arquivos de nível superior neste diretório:", "wantsToViewRecursive": "Roo quer visualizar recursivamente todos os arquivos neste diretório:", "didViewRecursive": "Roo visualizou recursivamente todos os arquivos neste diretório:", "wantsToViewDefinitions": "Roo quer visualizar nomes de definição de código-fonte usados neste diretório:", "didViewDefinitions": "Roo visualizou nomes de definição de código-fonte usados neste diretório:", "wantsToSearch": "Roo quer pesquisar neste diretório por <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON> pesquisou neste diretório por <code>{{regex}}</code>:"}, "commandOutput": "Saída do comando", "response": "Resposta", "arguments": "Argumentos", "mcp": {"wantsToUseTool": "Roo quer usar uma ferramenta no servidor MCP {{serverName}}:", "wantsToAccessResource": "Roo quer acessar um recurso no servidor MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Roo quer mudar para o modo <code>{{mode}}</code>", "wantsToSwitchWithReason": "Roo quer mudar para o modo <code>{{mode}}</code> porque: {{reason}}", "didSwitch": "<PERSON>oo mudou para o modo <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON>oo mudou para o modo <code>{{mode}}</code> porque: {{reason}}"}, "subtasks": {"wantsToCreate": "Roo quer criar uma nova subtarefa no modo <code>{{mode}}</code>:", "wantsToFinish": "<PERSON><PERSON> quer finalizar esta subtarefa", "newTaskContent": "Instruções da subtarefa", "completionContent": "Subtarefa concluída", "resultContent": "Resultados da subtarefa", "defaultResult": "Por favor, continue com a próxima tarefa.", "completionInstructions": "Subtarefa concluída! Você pode revisar os resultados e sugerir correções ou próximos passos. Se tudo parecer bom, confirme para retornar o resultado à tarefa principal."}, "questions": {"hasQuestion": "Roo tem uma pergunta:"}, "taskCompleted": "Tarefa concluída", "powershell": {"issues": "Parece que você está tendo problemas com o Windows PowerShell, por favor veja este"}, "autoApprove": {"title": "Aprovação automática:", "none": "<PERSON><PERSON><PERSON><PERSON>", "description": "A aprovação automática permite que o Roo Code execute ações sem pedir permissão. Ative apenas para ações nas quais você confia totalmente. Configuração mais detalhada disponível nas <settingsLink>Configurações</settingsLink>."}, "reasoning": {"thinking": "Pensando", "seconds": "{{count}}s"}, "contextCondense": {"title": "Contexto condensado", "condensing": "Condensando contexto...", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "Copiar para entrada (ou Shift + clique)"}, "announcement": {"title": "🎉 Roo Code {{version}} Lançado", "description": "Roo Code {{version}} traz poderosos novos recursos e melhorias baseados no seu feedback.", "whatsNew": "O que há de novo", "feature1": "<bold>Modelos de Pré-visualização Gemini 2.5 Flash</bold>: Acesse os mais recentes modelos Gemini Flash para respostas mais rápidas e eficientes", "feature2": "<bold>Condensação Inteligente de Contexto</bold>: Novo botão no cabeçalho da tarefa permite condensar conteúdo de forma inteligente com feedback visual", "feature3": "<bold>Suporte YAML para Definições de Modo</bold>: Crie e personalize modos mais facilmente com suporte a YAML", "hideButton": "<PERSON><PERSON><PERSON><PERSON>", "detailsDiscussLinks": "Obtenha mais detalhes e participe da discussão no <discordLink>Discord</discordLink> e <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "Roo quer usar o navegador:", "consoleLogs": "Logs do console", "noNewLogs": "(Sem novos logs)", "screenshot": "Captura de tela do navegador", "cursor": "cursor", "navigation": {"step": "Passo {{current}} de {{total}}", "previous": "Anterior", "next": "Próximo"}, "sessionStarted": "Sessão do navegador iniciada", "actions": {"title": "Ação do navegador: ", "launch": "Iniciar nave<PERSON> em {{url}}", "click": "Clique ({{coordinate}})", "type": "Digitar \"{{text}}\"", "scrollDown": "Rolar para baixo", "scrollUp": "Rolar para cima", "close": "<PERSON><PERSON><PERSON>"}}, "codeblock": {"tooltips": {"expand": "Expandir bloco de código", "collapse": "Re<PERSON>lher bloco de código", "enable_wrap": "Ativar quebra de linha", "disable_wrap": "<PERSON>ati<PERSON> que<PERSON> de linha", "copy_code": "<PERSON><PERSON>r c<PERSON>"}}, "systemPromptWarning": "AVISO: Substituição personalizada de instrução do sistema ativa. Isso pode comprometer gravemente a funcionalidade e causar comportamento imprevisível.", "shellIntegration": {"title": "Aviso de execução de comando", "description": "Seu comando está sendo executado sem a integração de shell do terminal VSCode. Para suprimir este aviso, você pode desativar a integração de shell na seção <strong>Terminal</strong> das <settingsLink>configurações do Roo Code</settingsLink> ou solucionar problemas de integração do terminal VSCode usando o link abaixo.", "troubleshooting": "Clique aqui para a documentação de integração de shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limite de Solicitações Auto-aprovadas Atingido", "description": "<PERSON>oo atingiu o limite auto-aprovado de {{count}} solicitação(ões) de API. Deseja redefinir a contagem e prosseguir com a tarefa?", "button": "Redefinir e Continuar"}}}