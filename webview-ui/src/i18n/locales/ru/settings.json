{"common": {"save": "Сохранить", "done": "Готово", "cancel": "Отмена", "reset": "Сбросить", "select": "Выбрать", "add": "Добавить заголовок", "remove": "Удалить"}, "header": {"title": "Настройки", "saveButtonTooltip": "Сохранить изменения", "nothingChangedTooltip": "Изменений нет", "doneButtonTooltip": "Отменить несохранённые изменения и закрыть панель настроек"}, "unsavedChangesDialog": {"title": "Несохранённые изменения", "description": "Вы хотите отменить изменения и продолжить?", "cancelButton": "Отмена", "discardButton": "Отменить изменения"}, "sections": {"providers": "Провайдеры", "autoApprove": "Автоодобрение", "browser": "Доступ к компьютеру", "checkpoints": "Контрольные точки", "notifications": "Уведомления", "contextManagement": "Контекст", "terminal": "<PERSON>ер<PERSON><PERSON><PERSON><PERSON>", "experimental": "Экспериментальное", "language": "Язык", "about": "О Roo Code"}, "autoApprove": {"description": "Разрешить Roo автоматически выполнять операции без необходимости одобрения. Включайте эти параметры только если полностью доверяете ИИ и понимаете связанные с этим риски безопасности.", "readOnly": {"label": "Чтение", "description": "Если включено, Roo будет автоматически просматривать содержимое каталогов и читать файлы без необходимости нажимать кнопку \"Одобрить\".", "outsideWorkspace": {"label": "Включая файлы вне рабочей области", "description": "Разрешить Roo читать файлы вне текущей рабочей области без необходимости одобрения."}}, "write": {"label": "Запись", "description": "Автоматически создавать и редактировать файлы без необходимости одобрения", "delayLabel": "Задержка после записи для диагностики возможных проблем", "outsideWorkspace": {"label": "Включая файлы вне рабочей области", "description": "Разрешить Roo создавать и редактировать файлы вне текущей рабочей области без необходимости одобрения."}}, "browser": {"label": "Браузер", "description": "Автоматически выполнять действия в браузере без необходимости одобрения. Применяется только, если модель поддерживает использование компьютера"}, "retry": {"label": "Повтор", "description": "Автоматически повторять неудачные запросы к API при ошибке сервера", "delayLabel": "Задержка перед повтором запроса"}, "mcp": {"label": "MCP", "description": "Включить автоодобрение отдельных инструментов MCP в представлении MCP Servers (требуется включить как этот параметр, так и индивидуальный чекбокс инструмента \"Всегда разрешать\")"}, "modeSwitch": {"label": "Режим", "description": "Автоматически переключаться между разными режимами без необходимости одобрения"}, "subtasks": {"label": "Подзадачи", "description": "Разрешить создание и выполнение подзадач без необходимости одобрения"}, "execute": {"label": "Выполнение", "description": "Автоматически выполнять разрешённые команды терминала без необходимости одобрения", "allowedCommands": "Разрешённые авто-выполняемые команды", "allowedCommandsDescription": "Префиксы команд, которые могут быть автоматически выполнены при включённом параметре \"Всегда одобрять выполнение операций\". Добавьте * для разрешения всех команд (используйте с осторожностью).", "commandPlaceholder": "Введите префикс команды (например, 'git ')", "addButton": "Добавить"}, "apiRequestLimit": {"title": "Максимум запросов", "description": "Автоматически выполнять это количество API-запросов перед запросом разрешения на продолжение задачи.", "unlimited": "Без ограничений"}}, "providers": {"providerDocumentation": "Документация {{provider}}", "configProfile": "Профиль конфигурации", "description": "Сохраняйте различные конфигурации API для быстрого переключения между провайдерами и настройками.", "apiProvider": "Провайдер API", "model": "Модель", "nameEmpty": "Имя не может быть пустым", "nameExists": "Профиль с таким именем уже существует", "deleteProfile": "Удалить профиль", "invalidArnFormat": "Неверный формат ARN. Пожалуйста, проверьте примеры выше.", "enterNewName": "Введите новое имя", "addProfile": "Добавить профиль", "renameProfile": "Переименовать профиль", "newProfile": "Новый профиль конфигурации", "enterProfileName": "Введите имя профиля", "createProfile": "Создать профиль", "cannotDeleteOnlyProfile": "Нельзя удалить единственный профиль", "searchPlaceholder": "Поиск профилей", "noMatchFound": "Совпадений не найдено", "vscodeLmDescription": "API языковой модели VS Code позволяет запускать модели, предоставляемые другими расширениями VS Code (включая, но не ограничиваясь GitHub Copilot). Для начала установите расширения Copilot и Copilot Chat из VS Code Marketplace.", "awsCustomArnUse": "Введите действительный Amazon Bedrock ARN для используемой модели. Примеры формата:", "awsCustomArnDesc": "Убедитесь, что регион в ARN совпадает с выбранным выше регионом AWS.", "openRouterApiKey": "OpenRouter API-кл<PERSON>ч", "getOpenRouterApiKey": "Получить OpenRouter API-ключ", "apiKeyStorageNotice": "API-ключи хранятся безопасно в Secret Storage VSCode", "glamaApiKey": "Glama API-ключ", "getGlamaApiKey": "Получить Glama API-ключ", "useCustomBaseUrl": "Использовать пользовательский базовый URL", "useHostHeader": "Использовать пользовательский Host-заголовок", "useLegacyFormat": "Использовать устаревший формат OpenAI API", "customHeaders": "Пользовательские заголовки", "headerName": "Имя заголовка", "headerValue": "Значение заголовка", "noCustomHeaders": "Пользовательские заголовки не определены. Нажмите кнопку +, чтобы добавить.", "requestyApiKey": "Requesty API-ключ", "refreshModels": {"label": "Обновить модели", "hint": "Пожалуйста, откройте настройки заново, чтобы увидеть последние модели."}, "getRequestyApiKey": "Получить Requesty API-ключ", "openRouterTransformsText": "Сжимать подсказки и цепочки сообщений до размера контекста (<a>OpenRouter Transforms</a>)", "anthropicApiKey": "Anthropic API-ключ", "getAnthropicApiKey": "Получить Anthropic API-ключ", "anthropicUseAuthToken": "Передавать Anthropic API-ключ как Authorization-заголовок вместо X-Api-Key", "chutesApiKey": "Chutes API-ключ", "getChutesApiKey": "Получить Chutes API-ключ", "deepSeekApiKey": "DeepSeek API-кл<PERSON>ч", "getDeepSeekApiKey": "Получить DeepSeek API-ключ", "geminiApiKey": "Gemini API-кл<PERSON>ч", "getGroqApiKey": "Получить Groq API-ключ", "groqApiKey": "Groq API-ключ", "getGeminiApiKey": "Получить Gemini API-ключ", "openAiApiKey": "OpenAI API-ключ", "openAiBaseUrl": "Базовый URL", "getOpenAiApiKey": "Получить OpenAI API-ключ", "mistralApiKey": "Mistral API-ключ", "getMistralApiKey": "Получить Mistral / Codestral API-ключ", "codestralBaseUrl": "Базовый URL Codestral (опционально)", "codestralBaseUrlDesc": "Укажите альтернативный URL для модели Codestral.", "xaiApiKey": "xAI API-ключ", "getXaiApiKey": "Получить xAI API-ключ", "litellmApiKey": "API-ключ LiteLLM", "litellmBaseUrl": "Базовый URL LiteLLM", "awsCredentials": "AWS-учётные данные", "awsProfile": "Профиль AWS", "awsProfileName": "Имя профиля AWS", "awsAccessKey": "AWS Access Key", "awsSecretKey": "AWS Secret Key", "awsSessionToken": "AWS Session Token", "awsRegion": "Регион AWS", "awsCrossRegion": "Использовать кросс-региональный вывод", "enablePromptCaching": "Включить кэширование подсказок", "enablePromptCachingTitle": "Включить кэширование подсказок для повышения производительности и снижения затрат для поддерживаемых моделей.", "cacheUsageNote": "Примечание: если вы не видите использование кэша, попробуйте выбрать другую модель, а затем вернуться к нужной.", "vscodeLmModel": "Языковая модель", "vscodeLmWarning": "Внимание: это очень экспериментальная интеграция, поддержка провайдера может отличаться. Если возникает ошибка о неподдерживаемой модели — проблема на стороне провайдера.", "googleCloudSetup": {"title": "Для использования Google Cloud Vertex AI необходимо:", "step1": "1. Создайте аккаунт Google Cloud, включите Vertex AI API и нужные модели Claude.", "step2": "2. Установите Google Cloud CLI и настройте учетные данные по умолчанию.", "step3": "3. Или создайте сервисный аккаунт с ключом."}, "googleCloudCredentials": "Учётные данные Google Cloud", "googleCloudKeyFile": "Путь к ключу Google Cloud", "googleCloudProjectId": "ID проекта Google Cloud", "googleCloudRegion": "Регион Google Cloud", "lmStudio": {"baseUrl": "Базовый URL (опционально)", "modelId": "ID модели", "speculativeDecoding": "Включить speculative decoding", "draftModelId": "ID черновой модели", "draftModelDesc": "Черновая модель должна быть из той же семьи моделей для корректной работы speculative decoding.", "selectDraftModel": "Выбрать черновую модель", "noModelsFound": "Черновых моделей не найдено. Проверьте, что LM Studio запущен с включённым серверным режимом.", "description": "LM Studio позволяет запускать модели локально на вашем компьютере. Для начала ознакомьтесь с <a>кратким руководством</a>. Также необходимо включить <b>локальный сервер</b> LM Studio для работы с этим расширением. <span>Примечание:</span> Roo Code использует сложные подсказки и лучше всего работает с моделями Claude. Менее мощные модели могут работать некорректно."}, "ollama": {"baseUrl": "Базовый URL (опционально)", "modelId": "ID модели", "description": "Ollama позволяет запускать модели локально на вашем компьютере. Для начала ознакомьтесь с кратким руководством.", "warning": "Примечание: Roo Code использует сложные подсказки и лучше всего работает с моделями Claude. Менее мощные модели могут работать некорректно."}, "unboundApiKey": "Unbound API-ключ", "getUnboundApiKey": "Получить Unbound API-ключ", "unboundRefreshModelsSuccess": "Список моделей обновлен! Теперь вы можете выбрать из последних моделей.", "unboundInvalidApiKey": "Недействительный API-ключ. Пожалуйста, проверьте ваш API-ключ и попробуйте снова.", "humanRelay": {"description": "API-ключ не требуется, но пользователю нужно вручную копировать и вставлять информацию в веб-чат ИИ.", "instructions": "Во время использования появится диалоговое окно, и текущее сообщение будет скопировано в буфер обмена автоматически. Вам нужно вставить его в веб-версию ИИ (например, ChatGPT или Claude), затем скопировать ответ ИИ обратно в диалоговое окно и нажать кнопку подтверждения."}, "openRouter": {"providerRouting": {"title": "Маршрутизация провайдера OpenRouter", "description": "OpenRouter направляет запросы к лучшим доступным провайдерам для вашей модели. По умолчанию запросы балансируются между топовыми провайдерами для максимальной доступности. Однако вы можете выбрать конкретного провайдера для этой модели.", "learnMore": "Подробнее о маршрутизации провайдеров"}}, "customModel": {"capabilities": "Настройте возможности и стоимость вашей пользовательской модели, совместимой с OpenAI. Будьте осторожны при указании возможностей модели, это может повлиять на работу Roo Code.", "maxTokens": {"label": "Максимум токенов на вывод", "description": "Максимальное количество токенов, которые модель может сгенерировать в ответе. (Укажите -1, чтобы сервер сам определил максимум.)"}, "contextWindow": {"label": "Размер окна контекста", "description": "Общее количество токенов (вход + выход), которые модель может обработать."}, "imageSupport": {"label": "Поддержка изображений", "description": "Может ли эта модель обрабатывать и понимать изображения?"}, "computerUse": {"label": "Использование компьютера", "description": "Может ли эта модель взаимодействовать с браузером? (наприм<PERSON><PERSON>, Claude 3.7 Sonnet)."}, "promptCache": {"label": "Кэширование подсказок", "description": "Может ли эта модель кэшировать подсказки?"}, "pricing": {"input": {"label": "Цена за вход", "description": "Стоимость за миллион токенов во входном сообщении/подсказке. Влияет на стоимость отправки контекста и инструкций модели."}, "output": {"label": "Цена за вывод", "description": "Стоимость за миллион токенов в ответе модели. Влияет на стоимость генерируемого контента."}, "cacheReads": {"label": "Цена чтения из кэша", "description": "Стоимость за миллион токенов при чтении из кэша. Взимается при получении кэшированного ответа."}, "cacheWrites": {"label": "Цена записи в кэш", "description": "Стоимость за миллион токенов при записи в кэш. Взимается при первом кэшировании подсказки."}}, "resetDefaults": "Сбросить к значениям по умолчанию"}, "rateLimitSeconds": {"label": "Лимит скорости", "description": "Минимальное время между запросами к API."}, "reasoningEffort": {"label": "Усилия по рассуждению модели", "high": "Высокие", "medium": "Средние", "low": "Низкие"}, "setReasoningLevel": "Включить усилие рассуждения"}, "browser": {"enable": {"label": "Включить инструмент браузера", "description": "Если включено, Roo может использовать браузер для взаимодействия с сайтами при использовании моделей, поддерживающих работу с компьютером. <0>Подробнее</0>"}, "viewport": {"label": "Размер окна просмотра", "description": "Выберите размер окна для взаимодействия с браузером. Влияет на отображение и взаимодействие с сайтами.", "options": {"largeDesktop": "Большой рабочий стол (1280x800)", "smallDesktop": "Маленький рабочий стол (900x600)", "tablet": "Планшет (768x1024)", "mobile": "Мобильный (360x640)"}}, "screenshotQuality": {"label": "Качество скриншота", "description": "Настройте качество WebP для скриншотов браузера. Более высокие значения дают более чёткие изображения, но увеличивают расход токенов."}, "remote": {"label": "Использовать удалённое подключение к браузеру", "description": "Подключиться к Chrome с включённым удалённым дебагом (--remote-debugging-port=9222).", "urlPlaceholder": "Пользовательский URL (например, http://localhost:9222)", "testButton": "Проверить соединение", "testingButton": "Проверка...", "instructions": "Введите адрес DevTools Protocol или оставьте поле пустым для автоматического поиска локальных экземпляров Chrome. Кнопка проверки попробует пользовательский URL, если он указан, или выполнит автопоиск."}}, "checkpoints": {"enable": {"label": "Включить автоматические контрольные точки", "description": "Если включено, Roo будет автоматически создавать контрольные точки во время выполнения задач, что упрощает просмотр изменений или возврат к предыдущим состояниям. <0>Подробнее</0>"}}, "notifications": {"sound": {"label": "Включить звуковые эффекты", "description": "Если включено, Roo будет воспроизводить звуковые эффекты для уведомлений и событий.", "volumeLabel": "Громкость"}, "tts": {"label": "Включить озвучивание", "description": "Если включено, Roo будет озвучивать свои ответы с помощью преобразования текста в речь.", "speedLabel": "Скорость"}}, "contextManagement": {"description": "Управляйте, какая информация включается в окно контекста ИИ, что влияет на расход токенов и качество ответов", "openTabs": {"label": "Лимит контекста открытых вкладок", "description": "Максимальное количество открытых вкладок VSCode, включаемых в контекст. Большее значение даёт больше контекста, но увеличивает расход токенов."}, "workspaceFiles": {"label": "<PERSON>и<PERSON><PERSON>т контекста файлов рабочей области", "description": "Максимальное количество файлов, включаемых в детали текущей рабочей директории. Большее значение даёт больше контекста, но увеличивает расход токенов."}, "rooignore": {"label": "Показывать .rooignore-файлы в списках и поиске", "description": "Если включено, файлы, совпадающие с шаблонами в .rooignore, будут отображаться в списках с символом замка. Если выключено, такие файлы полностью скрываются из списков и поиска."}, "maxReadFile": {"label": "Порог автообрезки при чтении файла", "description": "Roo читает столько строк, если модель не указала явно начало/конец. Если число меньше общего количества строк в файле, Roo создаёт индекс определений кода по строкам. Особые случаи: -1 — Roo читает весь файл (без индексации), 0 — не читает строки, а создаёт только минимальный индекс. Меньшие значения минимизируют начальный контекст, позволяя точнее читать нужные диапазоны строк. Явные запросы начала/конца не ограничиваются этим параметром.", "lines": "строк", "always_full_read": "Всегда читать весь файл"}}, "terminal": {"basic": {"label": "Настройки терминала: Основные", "description": "Основные настройки терминала"}, "advanced": {"label": "Настройки терминала: Расширенные", "description": "Следующие параметры могут потребовать перезапуск терминала для применения настроек."}, "outputLineLimit": {"label": "Лимит вывода терминала", "description": "Максимальное количество строк, включаемых в вывод терминала при выполнении команд. При превышении строки из середины будут удаляться для экономии токенов. <0>Подробнее</0>"}, "shellIntegrationTimeout": {"label": "Таймаут интеграции оболочки терминала", "description": "Максимальное время ожидания инициализации интеграции оболочки перед выполнением команд. Для пользователей с долгим стартом shell это значение можно увеличить, если появляются ошибки \"Shell Integration Unavailable\". <0>Подробнее</0>"}, "shellIntegrationDisabled": {"label": "Отключить интеграцию оболочки терминала", "description": "Включите это, если команды терминала не работают должным образом или вы видите ошибки 'Shell Integration Unavailable'. Это использует более простой метод выполнения команд, обходя некоторые расширенные функции терминала. <0>Подробнее</0>"}, "commandDelay": {"label": "Задержка команды терминала", "description": "Задержка в миллисекундах после выполнения команды. Значение по умолчанию 0 полностью отключает задержку. Это может помочь захватить весь вывод в терминалах с проблемами синхронизации. Обычно реализуется установкой `PROMPT_COMMAND='sleep N'`, в Powershell добавляется `start-sleep` в конец команды. Изначально было обходом бага VSCode #237208 и может не требоваться. <0>Подробнее</0>"}, "compressProgressBar": {"label": "Сжимать вывод прогресс-бара", "description": "Если включено, обрабатывает вывод терминала с возвратами каретки (\\r), имитируя отображение в реальном терминале. Промежуточные состояния прогресс-бара удаляются, остаётся только финальное, что экономит место в контексте. <0>Подробнее</0>"}, "powershellCounter": {"label": "Включить обходчик счётчика PowerShell", "description": "Если включено, добавляет счётчик к командам PowerShell для корректного выполнения. Помогает при проблемах с захватом вывода в терминалах PowerShell. <0>Подробнее</0>"}, "zshClearEolMark": {"label": "Очищать метку конца строки ZSH", "description": "Если включено, очищает PROMPT_EOL_MARK в zsh, чтобы избежать проблем с интерпретацией вывода, когда он заканчивается специальными символами типа '%'. <0>Подробнее</0>"}, "zshOhMy": {"label": "Включить интеграцию Oh My Zsh", "description": "Если включено, устанавливает ITERM_SHELL_INTEGRATION_INSTALLED=Yes для поддержки функций интеграции Oh My Zsh. Применение этой настройки может потребовать перезапуска IDE. <0>Подробнее</0>"}, "zshP10k": {"label": "Включить интеграцию Powerlevel10k", "description": "Если включено, устанавливает POWERLEVEL9K_TERM_SHELL_INTEGRATION=true для поддержки функций Powerlevel10k. <0>Подробнее</0>"}, "zdotdir": {"label": "Включить обработку ZDOTDIR", "description": "Если включено, создаёт временную директорию для ZDOTDIR для корректной интеграции zsh. Это обеспечивает корректную работу интеграции VSCode с zsh, сохраняя вашу конфигурацию. <0>Подробнее</0>"}, "inheritEnv": {"label": "Наследовать переменные среды", "description": "Если включено, терминал будет наследовать переменные среды от родительского процесса VSCode, такие как настройки интеграции оболочки, определённые в профиле пользователя. Напрямую переключает глобальную настройку VSCode `terminal.integrated.inheritEnv`. <0>Подробнее</0>"}}, "advanced": {"diff": {"label": "Включить редактирование через диффы", "description": "Если включено, Roo сможет быстрее редактировать файлы и автоматически отклонять усечённые полные записи. Лучше всего работает с последней моделью Claude 3.7 Sonnet.", "strategy": {"label": "Стратегия диффа", "options": {"standard": "Стандар<PERSON>ная (один блок)", "multiBlock": "Экспериментально: Мультиблочный дифф", "unified": "Экспериментально: Унифицированный дифф"}, "descriptions": {"standard": "Стандартная стратегия применяет изменения к одному блоку кода за раз.", "unified": "Унифицированная стратегия использует несколько подходов к применению диффов и выбирает лучший.", "multiBlock": "Мультиблочная стратегия позволяет обновлять несколько блоков кода в файле за один запрос."}}, "matchPrecision": {"label": "Точность совпадения", "description": "Этот ползунок управляет точностью совпадения секций кода при применении диффов. Меньшие значения позволяют более гибкое совпадение, но увеличивают риск неверной замены. Используйте значения ниже 100% с осторожностью."}}}, "experimental": {"warning": "⚠️", "autoCondenseContextPercent": {"label": "Порог для запуска интеллектуального сжатия контекста", "description": "Когда контекстное окно достигает этого порога, Roo автоматически его сожмёт."}, "AUTO_CONDENSE_CONTEXT": {"name": "Автоматически запускать интеллектуальное сжатие контекста", "description": "Интеллектуальное сжатие контекста использует вызов LLM для обобщения предыдущего разговора, когда контекстное окно задачи достигает заданного порога, вместо удаления старых сообщений при заполнении контекста."}, "DIFF_STRATEGY_UNIFIED": {"name": "Использовать экспериментальную стратегию унифицированного диффа", "description": "Включает экспериментальную стратегию унифицированного диффа. Может уменьшить количество повторных попыток из-за ошибок модели, но может привести к неожиданному поведению или неверным правкам. Включайте только если готовы внимательно проверять все изменения."}, "SEARCH_AND_REPLACE": {"name": "Использовать экспериментальный инструмент поиска и замены", "description": "Включает экспериментальный инструмент поиска и замены, позволяя Roo заменять несколько вхождений за один запрос."}, "INSERT_BLOCK": {"name": "Использовать экспериментальный инструмент вставки контента", "description": "Включает экспериментальный инструмент вставки контента, позволяя Roo вставлять контент по номеру строки без создания диффа."}, "POWER_STEERING": {"name": "Использовать экспериментальный режим \"power steering\"", "description": "Если включено, Roo будет чаще напоминать модели детали текущего режима. Это приведёт к более строгому следованию ролям и инструкциям, но увеличит расход токенов."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Использовать экспериментальный мультиблочный инструмент диффа", "description": "Если включено, Roo будет использовать мультиблочный инструмент диффа, пытаясь обновить несколько блоков кода за один запрос."}}, "promptCaching": {"label": "Отключить кэширование промптов", "description": "Если отмечено, Roo не будет использовать кэширование промптов для этой модели."}, "temperature": {"useCustom": "Использовать пользовательскую температуру", "description": "Управляет случайностью ответов модели.", "rangeDescription": "Более высокие значения делают ответы более случайными, низкие — более детерминированными."}, "modelInfo": {"supportsImages": "Поддерживает изображения", "noImages": "Не поддерживает изображения", "supportsComputerUse": "Поддерживает использование компьютера", "noComputerUse": "Не поддерживает использование компьютера", "supportsPromptCache": "Поддерживает кэширование подсказок", "noPromptCache": "Не поддерживает кэширование подсказок", "maxOutput": "Максимум вывода", "inputPrice": "Цена за вход", "outputPrice": "Цена за вывод", "cacheReadsPrice": "Цена чтения из кэша", "cacheWritesPrice": "Цена записи в кэш", "enableStreaming": "Включить потоковую передачу", "enableR1Format": "Включить параметры модели R1", "enableR1FormatTips": "Необходимо включить при использовании моделей R1 (например, QWQ), чтобы избежать ошибок 400", "useAzure": "Использовать Azure", "azureApiVersion": "Установить версию API Azure", "gemini": {"freeRequests": "* Бесплатно до {{count}} запросов в минуту. Далее тарификация зависит от размера подсказки.", "pricingDetails": "Подробнее о ценах.", "billingEstimate": "* Счёт — приблизительный, точная стоимость зависит от размера подсказки."}}, "modelPicker": {"automaticFetch": "Расширение автоматически получает актуальный список моделей на <serviceLink>{{serviceName}}</serviceLink>. Если не уверены, что выбрать, Roo Code лучше всего работает с <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Также попробуйте поискать \"free\" для бесплатных вариантов.", "label": "Модель", "searchPlaceholder": "Поиск", "noMatchFound": "Совпадений не найдено", "useCustomModel": "Использовать пользовательскую: {{modelId}}"}, "footer": {"feedback": "Если у вас есть вопросы или предложения, откройте issue на <githubLink>github.com/RooCodeInc/Roo-Code</githubLink> или присоединяйтесь к <redditLink>reddit.com/r/RooCode</redditLink> или <discordLink>discord.gg/roocode</discordLink>", "telemetry": {"label": "Разрешить анонимную отправку ошибок и статистики использования", "description": "Помогите улучшить Roo Code, отправляя анонимные данные об ошибках и использовании. Код, подсказки и личная информация не отправляются. Подробнее — в политике конфиденциальности."}, "settings": {"import": "Импорт", "export": "Экспорт", "reset": "Сбросить"}}, "thinkingBudget": {"maxTokens": "Максимум токенов", "maxThinkingTokens": "Максимум токенов на размышления"}, "validation": {"apiKey": "Вы должны указать действительный API-ключ.", "awsRegion": "Вы должны выбрать регион для использования с Amazon Bedrock.", "googleCloud": "Вы должны указать действительный Project ID и регион Google Cloud.", "modelId": "Вы должны указать действительный ID модели.", "modelSelector": "Вы должны указать действительный селектор модели.", "openAi": "Вы должны указать действительный базовый URL, API-ключ и ID модели.", "arn": {"invalidFormat": "Неверный формат ARN. Проверьте требования к формату.", "regionMismatch": "Внимание: регион в вашем ARN ({{arnRegion}}) не совпадает с выбранным регионом ({{region}}). Это может вызвать проблемы с доступом. Провайдер будет использовать регион из ARN."}, "modelAvailability": "ID модели ({{modelId}}), который вы указали, недоступен. Пожалуйста, выберите другую модель."}, "placeholders": {"apiKey": "Введите API-ключ...", "profileName": "Введите имя профиля", "accessKey": "Введите Access Key...", "secretKey": "Введите Secret Key...", "sessionToken": "Введите Session Token...", "credentialsJson": "Введите Credentials JSON...", "keyFilePath": "Введите путь к ключу...", "projectId": "Введите Project ID...", "customArn": "Введите ARN (например, arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Введите базовый URL...", "modelId": {"lmStudio": "например, meta-llama-3.1-8b-instruct", "lmStudioDraft": "например, lmstudio-community/llama-3.2-1b-instruct", "ollama": "например, llama3.1"}, "numbers": {"maxTokens": "например, 4096", "contextWindow": "например, 128000", "inputPrice": "например, 0.0001", "outputPrice": "например, 0.0002", "cacheWritePrice": "например, 0.00005"}}, "defaults": {"ollamaUrl": "По умолчанию: http://localhost:11434", "lmStudioUrl": "По умолчанию: http://localhost:1234", "geminiUrl": "По умолчанию: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "Пользовательский ARN", "useCustomArn": "Использовать пользовательский ARN..."}}