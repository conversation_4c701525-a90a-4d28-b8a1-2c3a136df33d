{"title": "提示詞", "done": "完成", "modes": {"title": "模式", "createNewMode": "建立新模式", "editModesConfig": "編輯模式設定", "editGlobalModes": "編輯全域模式", "editProjectModes": "編輯專案模式 (.roomodes)", "createModeHelpText": "模式是 Roo 的專屬角色，用於客製化其行為。<0>了解如何使用模式</0>或<1>自訂模式。</1>", "selectMode": "搜尋模式"}, "apiConfiguration": {"title": "API 設定", "select": "選擇要用於此模式的 API 設定"}, "tools": {"title": "可用工具", "builtInModesText": "內建模式的工具無法修改", "editTools": "編輯工具", "doneEditing": "完成編輯", "allowedFiles": "允許的檔案：", "toolNames": {"read": "讀取檔案", "edit": "編輯檔案", "browser": "使用瀏覽器", "command": "執行命令", "mcp": "使用 MCP"}, "noTools": "無"}, "roleDefinition": {"title": "角色定義", "resetToDefault": "重設為預設值", "description": "定義此模式下 Roo 的專業知識和個性。此描述會形塑 Roo 如何展現自己並處理工作。"}, "whenToUse": {"title": "使用時機（選用）", "description": "描述何時應使用此模式。這有助於 Orchestrator 為任務選擇適當的模式。", "resetToDefault": "重設「使用時機」描述為預設值"}, "customInstructions": {"title": "模式專屬自訂指令（選用）", "resetToDefault": "重設為預設值", "description": "為 {{modeName}} 模式新增專屬的行為指南。", "loadFromFile": "{{mode}} 模式的自訂指令也可以從工作區的 <span>.roo/rules-{{slug}}/</span> 資料夾載入（.roorules-{{slug}} 和 .clinerules-{{slug}} 已棄用並將很快停止運作）。"}, "globalCustomInstructions": {"title": "所有模式的自訂指令", "description": "這些指令適用於所有模式。它們提供了一組基本行為，可以透過下方的模式專屬自訂指令來強化。<0>了解更多</0>", "loadFromFile": "指令也可以從工作區的 <span>.roo/rules/</span> 資料夾載入（.roorules 和 .clinerules 已棄用並將很快停止運作）。"}, "systemPrompt": {"preview": "預覽系統提示詞", "copy": "複製系統提示詞到剪貼簿", "title": "系統提示詞（{{modeName}} 模式）"}, "supportPrompts": {"title": "輔助提示詞", "resetPrompt": "將 {{promptType}} 提示詞重設為預設值", "prompt": "提示詞", "enhance": {"apiConfiguration": "API 設定", "apiConfigDescription": "您可以選擇一個固定的 API 設定用於增強提示詞，或使用目前選擇的設定", "useCurrentConfig": "使用目前選擇的 API 設定", "testPromptPlaceholder": "輸入提示詞以測試增強效果", "previewButton": "預覽提示詞增強"}, "types": {"ENHANCE": {"label": "增強提示詞", "description": "使用提示詞增強功能取得針對您輸入的客製化建議或改進。這確保 Roo 能理解您的意圖並提供最佳的回應。可透過聊天中的 ✨ 圖示使用。"}, "EXPLAIN": {"label": "解釋程式碼", "description": "取得程式碼片段、函式或整個檔案的詳細解釋。有助於理解複雜程式碼或學習新模式。可在程式碼操作（編輯器中的燈泡圖示）和編輯器右鍵選單中使用。"}, "FIX": {"label": "修復問題", "description": "協助識別和解決錯誤、程式臭蟲或程式碼品質問題。提供逐步修復問題的指引。可在程式碼操作（編輯器中的燈泡圖示）和編輯器右鍵選單中使用。"}, "IMPROVE": {"label": "改進程式碼", "description": "在維持功能的同時，提供程式碼最佳化、最佳實踐和架構改進的建議。可在程式碼操作（編輯器中的燈泡圖示）和編輯器右鍵選單中使用。"}, "ADD_TO_CONTEXT": {"label": "新增至上下文", "description": "為目前工作或對話新增上下文。用於提供額外資訊或說明。可在程式碼操作（編輯器中的燈泡圖示）和編輯器右鍵選單中使用。"}, "TERMINAL_ADD_TO_CONTEXT": {"label": "新增終端機內容至上下文", "description": "將終端機輸出新增至目前工作或對話。用於提供命令輸出或記錄。可在終端機右鍵選單中使用。"}, "TERMINAL_FIX": {"label": "修復終端機命令", "description": "協助修復失敗或需要改進的終端機命令。可在終端機右鍵選單中使用。"}, "TERMINAL_EXPLAIN": {"label": "說明終端機命令", "description": "取得終端機命令及其輸出的詳細說明。可在終端機內容選單（選取終端機內容後按右鍵）使用。"}, "NEW_TASK": {"label": "開始新工作", "description": "開始一個新的工作。可在命令選擇區使用。"}}}, "advancedSystemPrompt": {"title": "進階：覆寫系統提示詞", "description": "<2>⚠️ 警告：</2> 此進階功能會略過安全防護。<1>使用前請閱讀！</1>透過在您的工作區中建立檔案 <span>.roo/system-prompt-{{slug}}</span> 來覆寫預設的系統提示詞。"}, "createModeDialog": {"title": "建立新模式", "close": "關閉", "name": {"label": "名稱", "placeholder": "輸入模式名稱"}, "slug": {"label": "識別符號", "description": "識別符號用於 URL 和檔案名稱。應使用小寫字母，且只能包含字母、數字和連字號。"}, "saveLocation": {"label": "儲存位置", "description": "選擇儲存此模式的位置。專案特定模式優先於全域模式。", "global": {"label": "全域", "description": "在所有工作區可用"}, "project": {"label": "專案特定 (.roomodes)", "description": "僅在此工作區可用，優先於全域模式"}}, "roleDefinition": {"label": "角色定義", "description": "定義此模式下 Roo 的專業知識和個性。"}, "whenToUse": {"label": "使用時機（選用）", "description": "提供清晰的描述，說明此模式最適合什麼時候使用，以及適合哪些類型的任務。"}, "tools": {"label": "可用工具", "description": "選擇此模式可使用的工具。"}, "customInstructions": {"label": "自訂指令（選用）", "description": "為此模式新增特定的行為指南。"}, "buttons": {"cancel": "取消", "create": "建立模式"}, "deleteMode": "刪除模式"}, "allFiles": "所有檔案"}