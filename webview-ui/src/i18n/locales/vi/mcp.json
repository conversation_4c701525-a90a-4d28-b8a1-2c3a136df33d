{"title": "<PERSON><PERSON><PERSON> chủ MCP", "done": "<PERSON><PERSON>", "description": "Bật Model Context Protocol (MCP) để Roo Code có thể dùng thêm công cụ và dịch vụ từ máy chủ bên ngoài. Đi<PERSON>u này mở rộng khả năng của Roo cho bạn. <0>Tìm hiểu thêm</0>", "enableToggle": {"title": "<PERSON><PERSON><PERSON> chủ MCP", "description": "Bật lên để Roo dùng công cụ từ các máy chủ MCP đã kết nối. Roo sẽ có nhiều khả năng hơn. Nếu không dùng các công cụ này, hãy tắt để tiết kiệm chi phí token API."}, "enableServerCreation": {"title": "<PERSON><PERSON><PERSON> t<PERSON>o m<PERSON> chủ MCP", "description": "<PERSON><PERSON>t lên để <PERSON>oo giúp bạn tạo <1>máy chủ MCP mới</1> tuỳ chỉnh. <0>Tìm hiểu về tạo máy chủ</0>", "hint": "Mẹo: <PERSON><PERSON> gi<PERSON>m chi phí token API, hãy tắt khi không cần <PERSON>oo tạo máy chủ MCP mới."}, "editGlobalMCP": "Chỉnh sửa MCP to<PERSON><PERSON> cục", "editProjectMCP": "Chỉnh sửa MCP dự án", "learnMoreEditingSettings": "<PERSON><PERSON>m hiểu thêm về chỉnh sửa file cài đặt MCP", "tool": {"alwaysAllow": "Lu<PERSON>n cho phép", "parameters": "<PERSON>ham s<PERSON>", "noDescription": "<PERSON><PERSON><PERSON><PERSON> có mô tả"}, "tabs": {"tools": "<PERSON><PERSON><PERSON> cụ", "resources": "<PERSON><PERSON><PERSON>", "errors": "Lỗi"}, "emptyState": {"noTools": "<PERSON><PERSON><PERSON><PERSON> tìm thấy công cụ", "noResources": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "noLogs": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhật ký", "noErrors": "<PERSON><PERSON><PERSON><PERSON> tìm thấy lỗi"}, "networkTimeout": {"label": "<PERSON>h<PERSON>i gian chờ mạng", "description": "<PERSON>h<PERSON><PERSON> gian tối đa chờ phản hồi từ máy chủ", "options": {"15seconds": "15 giây", "30seconds": "30 giây", "1minute": "1 phút", "5minutes": "5 phút", "10minutes": "10 phút", "15minutes": "15 phút", "30minutes": "30 phút", "60minutes": "60 phút"}}, "deleteDialog": {"title": "Xoá máy chủ MCP", "description": "Bạn chắc chắn muốn xoá máy chủ MCP \"{{serverName}}\"? Hành động này không thể hoàn tác.", "cancel": "Huỷ", "delete": "Xoá"}, "serverStatus": {"retrying": "<PERSON><PERSON> thử lại...", "retryConnection": "<PERSON><PERSON><PERSON> kết n<PERSON>i lại"}}