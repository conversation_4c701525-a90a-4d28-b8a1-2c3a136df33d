{"title": "MCP 服务器", "done": "完成", "description": "启用 Model Context Protocol (MCP)，让 Roo Code 可用外部服务器的工具和服务，扩展 Roo 的能力。<0>了解更多</0>", "enableToggle": {"title": "启用 MCP 服务器", "description": "开启后 Roo 可用已连接 MCP 服务器的工具，能力更强。不用这些工具时建议关闭，节省 API Token 费用。"}, "enableServerCreation": {"title": "启用 MCP 服务器创建", "description": "开启后 Roo 可帮你创建<1>新</1>自定义 MCP 服务器。<0>了解服务器创建</0>", "hint": "提示：不需要 Roo 创建新 MCP 服务器时建议关闭，减少 API Token 费用。"}, "editGlobalMCP": "编辑全局 MCP", "editProjectMCP": "编辑项目 MCP", "learnMoreEditingSettings": "了解如何编辑 MCP 设置文件", "tool": {"alwaysAllow": "始终允许", "parameters": "参数", "noDescription": "无描述"}, "tabs": {"tools": "工具", "resources": "资源", "errors": "错误"}, "emptyState": {"noTools": "未找到工具", "noResources": "未找到资源", "noLogs": "未找到日志", "noErrors": "未找到错误"}, "networkTimeout": {"label": "网络超时", "description": "服务器响应最大等待时间", "options": {"15seconds": "15秒", "30seconds": "30秒", "1minute": "1分钟", "5minutes": "5分钟", "10minutes": "10分钟", "15minutes": "15分钟", "30minutes": "30分钟", "60minutes": "60分钟"}}, "deleteDialog": {"title": "删除 MCP 服务器", "description": "确认删除 MCP 服务器 \"{{serverName}}\"？此操作不可逆。", "cancel": "取消", "delete": "删除"}, "serverStatus": {"retrying": "重试中...", "retryConnection": "重试连接"}}