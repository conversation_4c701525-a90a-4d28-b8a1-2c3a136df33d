{"title": "Serwery MCP", "done": "<PERSON><PERSON><PERSON>", "description": "Włącz Model Context Protocol (MCP), aby Roo Code mógł korzystać z dodatkowych narzędzi i usług z zewnętrznych serwerów. To rozszerza możliwości Roo. <0>Dowiedz się więcej</0>", "enableToggle": {"title": "Włącz serwery MCP", "description": "W<PERSON><PERSON><PERSON> to, aby Roo mógł korzystać z narzędzi połączonych serwerów MCP. <PERSON>je to Roo więcej możliwości. Jeś<PERSON> nie planujesz korzystać z tych dodatkowych narzędzi, wył<PERSON><PERSON> to, aby zmniejszyć koszty tokenów API."}, "enableServerCreation": {"title": "Włącz tworzenie serwerów MCP", "description": "<PERSON><PERSON><PERSON><PERSON> to, aby <PERSON>oo mógł pomóc ci tworzyć <1>nowe</1> niestandardowe serwery MCP. <0>Dowiedz się więcej o tworzeniu serwerów</0>", "hint": "Wskazówka: Aby zmniejszyć koszty tokenów API, wyłącz tę opcję, gdy nie prosisz Roo o utworzenie nowego serwera MCP."}, "editGlobalMCP": "Edytuj globalny MCP", "editProjectMCP": "Edytuj MCP projektu", "learnMoreEditingSettings": "Dowiedz się więcej o edycji plików ustawień MCP", "tool": {"alwaysAllow": "<PERSON><PERSON><PERSON> p<PERSON>walaj", "parameters": "Parametry", "noDescription": "Brak opisu"}, "tabs": {"tools": "Narzędzia", "resources": "<PERSON>as<PERSON><PERSON>", "errors": "Błędy"}, "emptyState": {"noTools": "Nie znaleziono narzędzi", "noResources": "Nie znaleziono zasobów", "noLogs": "Nie znaleziono logów", "noErrors": "Nie znaleziono błędów"}, "networkTimeout": {"label": "Limit c<PERSON>u sieci", "description": "Maksymalny czas oczekiwania na odpowiedzi serwera", "options": {"15seconds": "15 sekund", "30seconds": "30 sekund", "1minute": "1 minuta", "5minutes": "5 minut", "10minutes": "10 minut", "15minutes": "15 minut", "30minutes": "30 minut", "60minutes": "60 minut"}}, "deleteDialog": {"title": "<PERSON><PERSON><PERSON> serwer MCP", "description": "Czy na pewno chcesz usunąć serwer MCP \"{{serverName}}\"? Tej operacji nie można cofnąć.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Usuń"}, "serverStatus": {"retrying": "<PERSON><PERSON><PERSON><PERSON>e...", "retryConnection": "Ponów połączenie"}}